# Eliminador de Metadatos de Video

Un programa con interfaz gráfica para eliminar metadatos de archivos de video usando ffmpeg.

## Características

- **Instalación automática de dependencias**: El programa detecta automáticamente si ffmpeg está instalado y lo descarga e instala si es necesario.
- **Interfaz gráfica intuitiva**: Fácil de usar con botones para seleccionar archivos y carpetas.
- **Soporte para múltiples formatos**: Compatible con MP4, MOV y otros formatos de video.
- **Preservación de calidad**: Utiliza copia directa de streams cuando es posible para mantener la calidad original.
- **Registro de actividad**: Mantiene un log de todas las operaciones realizadas.

## Requisitos del Sistema

- **Python 3.6 o superior**
- **Windows** (el programa está optimizado para Windows)
- **Conexión a internet** (solo para la instalación automática de ffmpeg)

## Instalación y Uso

1. **Ejecutar el programa**:
   ```bash
   python eliminar_metadatos.py
   ```

2. **Primera ejecución**:
   - Si ffmpeg no está instalado, el programa te preguntará si quieres instalarlo automáticamente
   - La descarga es de aproximadamente 70MB y puede tomar unos minutos
   - Una vez instalado, ffmpeg se guardará localmente y no necesitará descargarse nuevamente

3. **Usar el programa**:
   - Haz clic en "Seleccionar Video" para elegir tu archivo
   - Haz clic en "Eliminar Metadatos" para procesar el video
   - Selecciona la carpeta donde quieres guardar el video sin metadatos
   - El archivo procesado tendrá el sufijo "_sin_metadatos"

## Instalación Automática de ffmpeg

El programa incluye un sistema de instalación automática que:

- **Detecta** si ffmpeg ya está instalado en el sistema
- **Descarga** automáticamente la versión más reciente de ffmpeg si no está disponible
- **Instala** ffmpeg localmente en la carpeta del programa
- **Configura** automáticamente el programa para usar la instalación local

### Proceso de Instalación Automática

1. El programa verifica si ffmpeg está disponible en el PATH del sistema
2. Si no está disponible, verifica si hay una instalación local
3. Si no encuentra ninguna instalación, muestra un diálogo preguntando si quieres instalar automáticamente
4. Si aceptas, descarga ffmpeg desde el sitio oficial y lo instala localmente
5. Una vez instalado, el programa está listo para usar

## Ventajas de la Instalación Automática

- **Sin configuración manual**: No necesitas descargar ni configurar ffmpeg manualmente
- **Instalación local**: ffmpeg se instala solo para este programa, sin afectar el sistema
- **Actualizaciones automáticas**: Siempre descarga la versión más reciente
- **Portabilidad**: Puedes mover la carpeta del programa a cualquier lugar y seguirá funcionando

## Archivos Generados

- `ffmpeg/`: Carpeta con la instalación local de ffmpeg (se crea automáticamente)
- `registro_proyecto.txt`: Log de todas las operaciones realizadas

## Solución de Problemas

### Error de conexión durante la descarga
- Verifica tu conexión a internet
- Intenta ejecutar el programa nuevamente
- Como alternativa, puedes instalar ffmpeg manualmente desde https://ffmpeg.org/download.html

### El programa no encuentra ffmpeg después de la instalación
- Reinicia el programa
- Verifica que la carpeta `ffmpeg/bin/` contiene el archivo `ffmpeg.exe`

### Error de permisos
- Ejecuta el programa como administrador si es necesario
- Verifica que tienes permisos de escritura en la carpeta del programa

## Notas Técnicas

- El programa utiliza solo librerías estándar de Python
- La descarga de ffmpeg se realiza desde el sitio oficial (gyan.dev)
- Se utiliza la versión "essentials" de ffmpeg que incluye los codecs más comunes
- Los metadatos se eliminan usando el parámetro `-map_metadata -1` de ffmpeg
