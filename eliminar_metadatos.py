import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess
import os

def seleccionar_archivo():
    """Abre un diálogo para seleccionar un archivo de video."""
    ruta_archivo = filedialog.askopenfilename(
        title="Seleccionar video",
        filetypes=(("Archivos MP4", "*.mp4"),
                   ("Archivos MOV", "*.mov"),
                   ("Todos los archivos", "*.*"))
    )
    if ruta_archivo:
        entrada_ruta_archivo.delete(0, tk.END)
        entrada_ruta_archivo.insert(0, ruta_archivo)

def eliminar_metadatos():
    """Elimina los metadatos del video seleccionado usando ffmpeg."""
    ruta_video_original = entrada_ruta_archivo.get()

    if not ruta_video_original:
        messagebox.showerror("Error", "Por favor, selecciona un archivo de video.")
        return

    if not os.path.exists(ruta_video_original):
        messagebox.showerror("Error", f"El archivo no existe: {ruta_video_original}")
        return

    directorio_salida = filedialog.askdirectory(title="Seleccionar carpeta de destino")
    if not directorio_salida:
        messagebox.showinfo("Cancelado", "Operación cancelada por el usuario.")
        return

    nombre_archivo_original, extension_archivo_original = os.path.splitext(os.path.basename(ruta_video_original))
    ruta_video_sin_metadatos = os.path.join(directorio_salida, f"{nombre_archivo_original}_sin_metadatos{extension_archivo_original}")

    # Comando ffmpeg para eliminar todos los metadatos y re-codificar si es necesario
    # -map_metadata -1: Elimina todos los streams de metadatos globales y por stream.
    # -c:v copy -c:a copy: Intenta copiar los streams de video y audio sin re-codificar.
    # Si la copia directa falla o el formato no lo permite, ffmpeg podría necesitar re-codificar.
    # Para forzar la re-codificación (puede tardar más y afectar la calidad) se pueden omitir -c:v copy -c:a copy
    # o especificar codecs, ej: -c:v libx264 -c:a aac
    comando = [
        'ffmpeg',
        '-i', ruta_video_original,
        '-map_metadata', '-1',
        '-c:v', 'copy',
        '-c:a', 'copy',
        ruta_video_sin_metadatos
    ]

    try:
        # Actualizar registro del proyecto
        with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
            f_registro.write(f"\nProcesando archivo: {ruta_video_original}\n")
            f_registro.write(f"Comando ffmpeg: {' '.join(comando)}\n")

        etiqueta_estado.config(text="Procesando... Por favor, espera.")
        ventana.update_idletasks()

        proceso = subprocess.Popen(comando, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
        stdout, stderr = proceso.communicate()

        if proceso.returncode == 0:
            messagebox.showinfo("Éxito", f"Metadatos eliminados. Video guardado en: {ruta_video_sin_metadatos}")
            # Actualizar registro del proyecto
            with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
                f_registro.write(f"Éxito al procesar: {ruta_video_original}. Video sin metadatos guardado en: {ruta_video_sin_metadatos}\n")
        else:
            error_msg = f"Error al procesar el video con ffmpeg:\n{stderr}"
            messagebox.showerror("Error de ffmpeg", error_msg)
            # Actualizar registro del proyecto
            with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
                f_registro.write(f"Error al procesar {ruta_video_original}: {stderr}\n")

    except FileNotFoundError:
        messagebox.showerror("Error", "ffmpeg no encontrado. Asegúrate de que ffmpeg esté instalado y en el PATH del sistema.")
        # Actualizar registro del proyecto
        with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
            f_registro.write("Error: ffmpeg no encontrado en el PATH.\n")
    except Exception as e:
        messagebox.showerror("Error inesperado", f"Ocurrió un error: {str(e)}")
        # Actualizar registro del proyecto
        with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
            f_registro.write(f"Error inesperado: {str(e)}\n")
    finally:
        etiqueta_estado.config(text="Listo.")

def verificar_ffmpeg():
    try:
        subprocess.run(['ffmpeg', '-version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except FileNotFoundError:
        messagebox.showerror("Error de Dependencia", "ffmpeg no encontrado. Por favor, instala ffmpeg y asegúrate de que esté en el PATH del sistema para que el programa funcione.\n\nPuedes descargarlo desde: https://ffmpeg.org/download.html")
        return False
    except Exception as e:
        messagebox.showerror("Error", f"Ocurrió un error al verificar ffmpeg: {e}")
        return False

# Configuración de la ventana principal de Tkinter
ventana = tk.Tk()
ventana.title("Eliminador de Metadatos de Video")
ventana.geometry("500x250")

# Verificar ffmpeg al inicio
if not verificar_ffmpeg():
    ventana.destroy()
else:
    # Etiqueta y campo de entrada para la ruta del archivo
    etiqueta_ruta_archivo = tk.Label(ventana, text="Ruta del video:")
    etiqueta_ruta_archivo.pack(pady=5)

    entrada_ruta_archivo = tk.Entry(ventana, width=60)
    entrada_ruta_archivo.pack(pady=5)

    # Botón para seleccionar archivo
    boton_seleccionar = tk.Button(ventana, text="Seleccionar Video", command=seleccionar_archivo)
    boton_seleccionar.pack(pady=10)

    # Botón para eliminar metadatos
    boton_eliminar = tk.Button(ventana, text="Eliminar Metadatos", command=eliminar_metadatos)
    boton_eliminar.pack(pady=10)

    # Etiqueta de estado
    etiqueta_estado = tk.Label(ventana, text="Listo.")
    etiqueta_estado.pack(pady=5)

    # Nota sobre ffmpeg
    etiqueta_nota_ffmpeg = tk.Label(ventana, text="Nota: Este programa requiere que ffmpeg esté instalado y accesible en el PATH del sistema.", fg="blue")
    etiqueta_nota_ffmpeg.pack(pady=10)

    ventana.mainloop()