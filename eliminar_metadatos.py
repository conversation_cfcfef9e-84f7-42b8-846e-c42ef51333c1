import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import subprocess
import os
import sys
import urllib.request
import zipfile
import shutil
import threading

def obtener_ruta_ffmpeg():
    """Obtiene la ruta donde debería estar instalado ffmpeg localmente."""
    directorio_actual = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(directorio_actual, "ffmpeg", "bin", "ffmpeg.exe")

def descargar_ffmpeg():
    """Descarga e instala ffmpeg automáticamente."""
    try:
        # URL de descarga de ffmpeg para Windows (versión estática)
        url_ffmpeg = "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
        directorio_actual = os.path.dirname(os.path.abspath(__file__))
        ruta_descarga = os.path.join(directorio_actual, "ffmpeg-temp.zip")
        ruta_extraccion = os.path.join(directorio_actual, "ffmpeg-temp")
        ruta_final = os.path.join(directorio_actual, "ffmpeg")

        # Descargar archivo
        urllib.request.urlretrieve(url_ffmpeg, ruta_descarga)

        # Extraer archivo ZIP
        with zipfile.ZipFile(ruta_descarga, 'r') as zip_ref:
            zip_ref.extractall(ruta_extraccion)

        # Encontrar la carpeta extraída (usualmente tiene un nombre con versión)
        carpetas_extraidas = [f for f in os.listdir(ruta_extraccion) if os.path.isdir(os.path.join(ruta_extraccion, f))]
        if carpetas_extraidas:
            carpeta_ffmpeg = os.path.join(ruta_extraccion, carpetas_extraidas[0])

            # Mover la carpeta a la ubicación final
            if os.path.exists(ruta_final):
                shutil.rmtree(ruta_final)
            shutil.move(carpeta_ffmpeg, ruta_final)

        # Limpiar archivos temporales
        os.remove(ruta_descarga)
        shutil.rmtree(ruta_extraccion)

        return True

    except Exception as e:
        print(f"Error al descargar ffmpeg: {str(e)}")
        return False

def instalar_ffmpeg_automaticamente():
    """Instala ffmpeg automáticamente con una ventana de progreso simple."""
    ventana_progreso = tk.Toplevel()
    ventana_progreso.title("Instalando ffmpeg")
    ventana_progreso.geometry("400x120")
    ventana_progreso.resizable(False, False)
    ventana_progreso.grab_set()  # Hacer modal

    # Centrar la ventana
    ventana_progreso.transient(ventana)

    etiqueta_info = tk.Label(ventana_progreso, text="Descargando e instalando ffmpeg...", font=("Arial", 10))
    etiqueta_info.pack(pady=15)

    barra_progreso = ttk.Progressbar(ventana_progreso, length=300, mode='indeterminate')
    barra_progreso.pack(pady=10)
    barra_progreso.start()

    etiqueta_estado = tk.Label(ventana_progreso, text="Por favor espera, esto puede tomar unos minutos...", font=("Arial", 8))
    etiqueta_estado.pack(pady=5)

    # Actualizar la ventana para mostrar el progreso
    ventana_progreso.update()

    # Ejecutar la descarga
    resultado = descargar_ffmpeg()

    # Detener la barra de progreso
    barra_progreso.stop()

    if resultado:
        etiqueta_estado.config(text="¡Instalación completada exitosamente!")
        barra_progreso['value'] = 100
    else:
        etiqueta_estado.config(text="Error durante la instalación")
        barra_progreso['value'] = 0

    ventana_progreso.update()
    ventana_progreso.after(2000, ventana_progreso.destroy)  # Cerrar después de 2 segundos
    ventana_progreso.wait_window()  # Esperar a que se cierre la ventana

    return resultado

def seleccionar_archivo():
    """Abre un diálogo para seleccionar un archivo de video."""
    ruta_archivo = filedialog.askopenfilename(
        title="Seleccionar video",
        filetypes=(("Archivos MP4", "*.mp4"),
                   ("Archivos MOV", "*.mov"),
                   ("Todos los archivos", "*.*"))
    )
    if ruta_archivo:
        entrada_ruta_archivo.delete(0, tk.END)
        entrada_ruta_archivo.insert(0, ruta_archivo)

def eliminar_metadatos():
    """Elimina los metadatos del video seleccionado usando ffmpeg."""
    ruta_video_original = entrada_ruta_archivo.get()

    if not ruta_video_original:
        messagebox.showerror("Error", "Por favor, selecciona un archivo de video.")
        return

    if not os.path.exists(ruta_video_original):
        messagebox.showerror("Error", f"El archivo no existe: {ruta_video_original}")
        return

    directorio_salida = filedialog.askdirectory(title="Seleccionar carpeta de destino")
    if not directorio_salida:
        messagebox.showinfo("Cancelado", "Operación cancelada por el usuario.")
        return

    nombre_archivo_original, extension_archivo_original = os.path.splitext(os.path.basename(ruta_video_original))
    ruta_video_sin_metadatos = os.path.join(directorio_salida, f"{nombre_archivo_original}_sin_metadatos{extension_archivo_original}")

    # Obtener la ruta de ffmpeg (local o del sistema)
    ruta_ffmpeg_local = obtener_ruta_ffmpeg()
    ejecutable_ffmpeg = ruta_ffmpeg_local if os.path.exists(ruta_ffmpeg_local) else 'ffmpeg'

    # Comando ffmpeg para eliminar todos los metadatos y re-codificar si es necesario
    # -map_metadata -1: Elimina todos los streams de metadatos globales y por stream.
    # -c:v copy -c:a copy: Intenta copiar los streams de video y audio sin re-codificar.
    # Si la copia directa falla o el formato no lo permite, ffmpeg podría necesitar re-codificar.
    # Para forzar la re-codificación (puede tardar más y afectar la calidad) se pueden omitir -c:v copy -c:a copy
    # o especificar codecs, ej: -c:v libx264 -c:a aac
    comando = [
        ejecutable_ffmpeg,
        '-i', ruta_video_original,
        '-map_metadata', '-1',
        '-c:v', 'copy',
        '-c:a', 'copy',
        ruta_video_sin_metadatos
    ]

    try:
        # Actualizar registro del proyecto
        with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
            f_registro.write(f"\nProcesando archivo: {ruta_video_original}\n")
            f_registro.write(f"Comando ffmpeg: {' '.join(comando)}\n")

        etiqueta_estado.config(text="Procesando... Por favor, espera.")
        ventana.update_idletasks()

        proceso = subprocess.Popen(comando, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
        _, stderr = proceso.communicate()

        if proceso.returncode == 0:
            messagebox.showinfo("Éxito", f"Metadatos eliminados. Video guardado en: {ruta_video_sin_metadatos}")
            # Actualizar registro del proyecto
            with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
                f_registro.write(f"Éxito al procesar: {ruta_video_original}. Video sin metadatos guardado en: {ruta_video_sin_metadatos}\n")
        else:
            error_msg = f"Error al procesar el video con ffmpeg:\n{stderr}"
            messagebox.showerror("Error de ffmpeg", error_msg)
            # Actualizar registro del proyecto
            with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
                f_registro.write(f"Error al procesar {ruta_video_original}: {stderr}\n")

    except FileNotFoundError:
        messagebox.showerror("Error", "ffmpeg no encontrado. Asegúrate de que ffmpeg esté instalado y en el PATH del sistema.")
        # Actualizar registro del proyecto
        with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
            f_registro.write("Error: ffmpeg no encontrado en el PATH.\n")
    except Exception as e:
        messagebox.showerror("Error inesperado", f"Ocurrió un error: {str(e)}")
        # Actualizar registro del proyecto
        with open("c:\\Cosas de programacion\\videostiktok\\registro_proyecto.txt", "a", encoding="utf-8") as f_registro:
            f_registro.write(f"Error inesperado: {str(e)}\n")
    finally:
        etiqueta_estado.config(text="Listo.")

def verificar_ffmpeg():
    """Verifica si ffmpeg está disponible y lo instala automáticamente si es necesario."""
    # Primero verificar si ffmpeg está instalado localmente
    ruta_ffmpeg_local = obtener_ruta_ffmpeg()
    if os.path.exists(ruta_ffmpeg_local):
        try:
            subprocess.run([ruta_ffmpeg_local, '-version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
            return True
        except Exception:
            pass  # Si falla, intentar con ffmpeg del sistema

    # Verificar si ffmpeg está en el PATH del sistema
    try:
        subprocess.run(['ffmpeg', '-version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except FileNotFoundError:
        # ffmpeg no encontrado, ofrecer instalación automática
        respuesta = messagebox.askyesno(
            "ffmpeg no encontrado",
            "ffmpeg no está instalado en tu sistema.\n\n"
            "¿Quieres que lo descargue e instale automáticamente?\n\n"
            "Esto descargará aproximadamente 70MB y puede tomar unos minutos."
        )

        if respuesta:
            exito = instalar_ffmpeg_automaticamente()
            if exito:
                messagebox.showinfo("Instalación exitosa", "ffmpeg se ha instalado correctamente. El programa está listo para usar.")
                return True
            else:
                messagebox.showerror(
                    "Error de instalación",
                    "No se pudo instalar ffmpeg automáticamente.\n\n"
                    "Puedes instalarlo manualmente desde: https://ffmpeg.org/download.html"
                )
                return False
        else:
            messagebox.showinfo(
                "Instalación cancelada",
                "Para usar este programa necesitas ffmpeg.\n\n"
                "Puedes instalarlo manualmente desde: https://ffmpeg.org/download.html"
            )
            return False
    except Exception as e:
        messagebox.showerror("Error", f"Ocurrió un error al verificar ffmpeg: {e}")
        return False

# Configuración de la ventana principal de Tkinter
ventana = tk.Tk()
ventana.title("Eliminador de Metadatos de Video")
ventana.geometry("500x250")

# Verificar ffmpeg al inicio
if not verificar_ffmpeg():
    ventana.destroy()
else:
    # Etiqueta y campo de entrada para la ruta del archivo
    etiqueta_ruta_archivo = tk.Label(ventana, text="Ruta del video:")
    etiqueta_ruta_archivo.pack(pady=5)

    entrada_ruta_archivo = tk.Entry(ventana, width=60)
    entrada_ruta_archivo.pack(pady=5)

    # Botón para seleccionar archivo
    boton_seleccionar = tk.Button(ventana, text="Seleccionar Video", command=seleccionar_archivo)
    boton_seleccionar.pack(pady=10)

    # Botón para eliminar metadatos
    boton_eliminar = tk.Button(ventana, text="Eliminar Metadatos", command=eliminar_metadatos)
    boton_eliminar.pack(pady=10)

    # Etiqueta de estado
    etiqueta_estado = tk.Label(ventana, text="Listo.")
    etiqueta_estado.pack(pady=5)

    # Nota sobre ffmpeg
    etiqueta_nota_ffmpeg = tk.Label(ventana, text="Nota: Este programa puede instalar ffmpeg automáticamente si no está disponible.", fg="blue")
    etiqueta_nota_ffmpeg.pack(pady=10)

    ventana.mainloop()