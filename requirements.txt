# Este programa utiliza solo librerías estándar de Python
# No se requieren dependencias externas de Python

# Dependencias del sistema:
# - Python 3.6 o superior
# - ffmpeg (se instala automáticamente si no está disponible)

# Librerías estándar utilizadas:
# - tkinter (GUI)
# - subprocess (ejecutar comandos)
# - os (operaciones del sistema)
# - sys (información del sistema)
# - urllib.request (descargar archivos)
# - zipfile (extraer archivos ZIP)
# - shutil (operaciones de archivos)
