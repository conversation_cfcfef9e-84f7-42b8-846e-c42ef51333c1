Inicio del proyecto: Eliminador de metadatos de video para TikTok.

Objetivo: Crear un programa que elimine todos los metadatos de un archivo de video para evitar problemas de copia en TikTok.

Investigación inicial:
- Se identificó `ffmpeg` como la herramienta más adecuada para la manipulación de metadatos de video.
- Bibliotecas de Python como `imageio` o `python-video-converter` pueden interactuar con `ffmpeg`.
- El enfoque será usar `subprocess` en Python para ejecutar comandos `ffmpeg`.

Desarrollo del programa:
- Se ha creado el script `eliminar_metadatos.py` utilizando `tkinter` para la interfaz gráfica y `subprocess` para ejecutar `ffmpeg`.
- El programa permite seleccionar un archivo de video, elegir una ubicación de salida y elimina los metadatos utilizando el comando `ffmpeg -i input.mp4 -map_metadata -1 -c:v copy -c:a copy output_sin_metadatos.mp4`.

Instrucciones de uso:
1. Asegúrate de tener Python instalado en tu sistema.
2. Ejecuta el script `eliminar_metadatos.py` con Python: `python eliminar_metadatos.py`.
3. Al iniciar, el programa verificará automáticamente si `ffmpeg` está instalado y accesible en el PATH. Si no lo está, te mostrará un mensaje con instrucciones para descargarlo.
4. Una vez que `ffmpeg` esté configurado correctamente, en la ventana que aparece, haz clic en 'Seleccionar Video' para elegir el archivo de video al que deseas eliminar los metadatos.
5. Haz clic en 'Eliminar Metadatos' y selecciona la carpeta donde quieres guardar el video procesado.
6. El nuevo video sin metadatos se guardará en la ubicación especificada con el sufijo '_sin_metadatos'.
Procesando archivo: C:/Users/<USER>/Downloads/Clutch Darius Quadra 👀 - twitch.tv-RaiderGO#leagueoflegends #leagueoflegendsmemes #leagueoflegendsfunny #lolreels #leaguereels #lolhighlights #lolgameplay #lolmoments #lolclips #soloqueue #lolcontent #riotgames #l.mp4
Comando ffmpeg: ffmpeg -i C:/Users/<USER>/Downloads/Clutch Darius Quadra 👀 - twitch.tv-RaiderGO#leagueoflegends #leagueoflegendsmemes #leagueoflegendsfunny #lolreels #leaguereels #lolhighlights #lolgameplay #lolmoments #lolclips #soloqueue #lolcontent #riotgames #l.mp4 -map_metadata -1 -c:v copy -c:a copy C:/Users/<USER>/Desktop\Clutch Darius Quadra 👀 - twitch.tv-RaiderGO#leagueoflegends #leagueoflegendsmemes #leagueoflegendsfunny #lolreels #leaguereels #lolhighlights #lolgameplay #lolmoments #lolclips #soloqueue #lolcontent #riotgames #l_sin_metadatos.mp4
Error: ffmpeg no encontrado en el PATH.
